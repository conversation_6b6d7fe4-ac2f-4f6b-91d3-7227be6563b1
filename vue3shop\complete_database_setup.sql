-- 完整的数据库设置SQL脚本
-- 包含建表和示例数据
-- 数据库：springbootds

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS springbootds DEFAULT CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE springbootds;

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 用户表
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `register_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `status` int(1) DEFAULT '0' COMMENT '用户状态(0-正常,1-禁用)',
  `avatar` varchar(255) DEFAULT NULL COMMENT '用户头像地址',
  `address` varchar(255) DEFAULT NULL COMMENT '收货地址',
  `nickname` varchar(50) DEFAULT NULL COMMENT '用户昵称',
  `role` varchar(20) DEFAULT 'CUSTOMER' COMMENT '用户角色(CUSTOMER-普通用户,MANUFACTURER-生产商,ADMIN-管理员)',
  `manufacturer_id` int(11) DEFAULT NULL COMMENT '生产商ID(仅当role为MANUFACTURER时有值)',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_manufacturer_id` (`manufacturer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ----------------------------
-- 2. 生产商表
-- ----------------------------
DROP TABLE IF EXISTS `manufacturer`;
CREATE TABLE `manufacturer` (
  `manufacturer_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '生产商ID',
  `manufacturer_name` varchar(100) NOT NULL COMMENT '生产商名称',
  `description` text COMMENT '生产商描述',
  `logo_url` varchar(255) DEFAULT NULL COMMENT '生产商logo',
  `contact_info` varchar(255) DEFAULT NULL COMMENT '联系方式',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `website` varchar(255) DEFAULT NULL COMMENT '官网',
  `status` int(1) DEFAULT '0' COMMENT '状态(0-正常,1-禁用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` int(11) NOT NULL COMMENT '创建者用户ID',
  PRIMARY KEY (`manufacturer_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_user` (`create_user`),
  KEY `idx_manufacturer_name` (`manufacturer_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产商表';

-- ----------------------------
-- 3. 商品分类表
-- ----------------------------
DROP TABLE IF EXISTS `category`;
CREATE TABLE `category` (
  `category_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(50) NOT NULL COMMENT '分类名称',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `create_user` int(11) NOT NULL COMMENT '创建用户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`category_id`),
  KEY `idx_create_user` (`create_user`),
  KEY `idx_category_name` (`category_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表';

-- ----------------------------
-- 4. 商品表
-- ----------------------------
DROP TABLE IF EXISTS `product`;
CREATE TABLE `product` (
  `product_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称',
  `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
  `manufacturer_id` int(11) DEFAULT NULL COMMENT '生产商ID',
  `price` decimal(10,2) DEFAULT NULL COMMENT '价格',
  `stock_quantity` int(11) DEFAULT '0' COMMENT '库存数量',
  `description` text COMMENT '商品描述',
  `image_path` varchar(255) DEFAULT NULL COMMENT '商品图片路径',
  `status` int(1) DEFAULT '0' COMMENT '商品状态(0-正常,1-下架)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`product_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_manufacturer_id` (`manufacturer_id`),
  KEY `idx_product_name` (`product_name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

-- ----------------------------
-- 5. 购物车表
-- ----------------------------
DROP TABLE IF EXISTS `cart`;
CREATE TABLE `cart` (
  `cart_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '购物车ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `product_id` int(11) NOT NULL COMMENT '商品ID',
  `quantity` int(11) DEFAULT '1' COMMENT '数量',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`cart_id`),
  UNIQUE KEY `uk_user_product` (`user_id`,`product_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='购物车表';

-- ----------------------------
-- 6. 用户购买记录表
-- ----------------------------
DROP TABLE IF EXISTS `user_purchase`;
CREATE TABLE `user_purchase` (
  `purchase_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '购买记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `product_id` int(11) NOT NULL COMMENT '商品ID',
  `quantity` int(11) NOT NULL COMMENT '购买数量',
  `purchase_price` decimal(10,2) NOT NULL COMMENT '购买价格',
  `total_amount` decimal(10,2) NOT NULL COMMENT '总金额',
  `purchase_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '购买时间',
  `status` int(1) DEFAULT '0' COMMENT '订单状态(0-待支付,1-已支付,2-已发货,3-已完成,4-已取消)',
  PRIMARY KEY (`purchase_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_purchase_time` (`purchase_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户购买记录表';

-- ----------------------------
-- 7. 订单表
-- ----------------------------
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `product_id` int(11) NOT NULL COMMENT '商品ID',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称',
  `price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `quantity` int(11) DEFAULT '1' COMMENT '购买数量',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `status` int(1) DEFAULT '0' COMMENT '订单状态(0-待支付,1-已支付,2-已发货,3-已完成,4-已取消)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `ship_time` datetime DEFAULT NULL COMMENT '发货时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `order_no` varchar(50) DEFAULT NULL COMMENT '订单号',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- ----------------------------
-- 8. 收藏表
-- ----------------------------
DROP TABLE IF EXISTS `collect`;
CREATE TABLE `collect` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `product_id` int(11) NOT NULL COMMENT '商品ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_product` (`user_id`,`product_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品收藏表';

-- ----------------------------
-- 9. 购买记录表
-- ----------------------------
DROP TABLE IF EXISTS `user_purchase`;
CREATE TABLE `user_purchase` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '购买记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `product_id` int(11) NOT NULL COMMENT '商品ID',
  `purchase_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '购买时间',
  `price` decimal(10,2) NOT NULL COMMENT '购买价格',
  `quantity` int(11) DEFAULT '1' COMMENT '购买数量',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_purchase_time` (`purchase_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户购买记录表';

-- ----------------------------
-- 插入示例数据
-- ----------------------------

-- 管理员用户
INSERT INTO `user` (`username`, `password`, `email`, `phone`, `register_time`, `status`, `role`, `nickname`, `address`) VALUES
('admin', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800000000', NOW(), 0, 'ADMIN', '系统管理员', '管理员地址');

-- 生产商数据
INSERT INTO `manufacturer` (`manufacturer_name`, `description`, `logo_url`, `contact_info`, `address`, `website`, `status`, `create_user`) VALUES
('华硕科技', '华硕是全球领先的3C解决方案提供商，致力于为个人和企业用户提供最具创新价值的产品及应用方案。', 'https://example.com/logos/asus.png', '************', '台湾台北市北投区立业街2号', 'https://www.asus.com.cn', 0, 1),
('微星科技', 'MSI微星科技为全球前三大主机板制造商，并跨足到显示卡、笔记本电脑、服务器等领域。', 'https://example.com/logos/msi.png', '************', '台湾新北市中和区立德街69号', 'https://cn.msi.com', 0, 1),
('技嘉科技', '技嘉科技是台湾的电脑硬件生产商，以主板和显卡为主要产品。', 'https://example.com/logos/gigabyte.png', '************', '台湾台北市内湖区瑞光路581号', 'https://www.gigabyte.cn', 0, 1);

-- 生产商用户
INSERT INTO `user` (`username`, `password`, `email`, `phone`, `register_time`, `status`, `role`, `nickname`, `address`, `manufacturer_id`) VALUES
('asus_manager', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800001001', NOW(), 0, 'MANUFACTURER', '华硕管理员', '华硕公司地址', 1),
('msi_manager', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800001002', NOW(), 0, 'MANUFACTURER', '微星管理员', '微星公司地址', 2),
('gigabyte_manager', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800001003', NOW(), 0, 'MANUFACTURER', '技嘉管理员', '技嘉公司地址', 3);

-- 普通用户
INSERT INTO `user` (`username`, `password`, `email`, `phone`, `register_time`, `status`, `role`, `nickname`, `address`) VALUES
('user001', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800000001', NOW(), 0, 'CUSTOMER', '张三', '北京市朝阳区'),
('user002', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '13800000002', NOW(), 0, 'CUSTOMER', '李四', '上海市浦东新区');

-- 商品分类
INSERT INTO `category` (`category_name`, `description`, `create_user`) VALUES
('CPU', 'Central Processing Unit 中央处理器', 1),
('主板', 'Motherboard 主板', 1),
('显卡', 'Graphics Card 显卡', 1),
('内存', 'Memory 内存条', 1),
('DIY电脑', 'DIY Computer 组装电脑', 1);

-- 商品数据
INSERT INTO `product` (`product_name`, `category_id`, `manufacturer_id`, `price`, `stock_quantity`, `description`, `image_path`) VALUES
('华硕ROG STRIX Z790-E主板', 2, 1, 2899.00, 30, '华硕ROG玩家国度Z790主板，支持Intel第13代处理器', 'https://example.com/products/asus-z790.jpg'),
('微星RTX 4080显卡', 3, 2, 8999.00, 25, '微星RTX 4080显卡，16GB GDDR6X显存', 'https://example.com/products/msi-rtx4080.jpg'),
('技嘉B650主板', 2, 3, 1599.00, 55, '技嘉B650主板，支持AMD锐龙7000系列', 'https://example.com/products/gigabyte-b650.jpg'),
('华硕游戏主机', 5, 1, 25999.00, 10, 'i9-13900K + RTX 4090 + 32GB DDR5，顶级游戏配置', 'https://example.com/products/asus-gaming-pc.jpg');

-- 订单数据
INSERT INTO `orders` (`user_id`, `product_id`, `product_name`, `price`, `quantity`, `total_amount`, `status`, `create_time`, `pay_time`, `order_no`) VALUES
(4, 1, '华硕ROG STRIX Z790-E主板', 2899.00, 1, 2899.00, 1, '2024-01-15 10:30:00', '2024-01-15 10:35:00', 'ORD20240115001'),
(5, 2, '微星RTX 4080显卡', 8999.00, 1, 8999.00, 2, '2024-01-16 14:20:00', '2024-01-16 14:25:00', 'ORD20240116001'),
(4, 3, '技嘉B650主板', 1599.00, 2, 3198.00, 3, '2024-01-17 09:15:00', '2024-01-17 09:20:00', 'ORD20240117001'),
(5, 4, '华硕游戏主机', 25999.00, 1, 25999.00, 0, '2024-01-18 16:45:00', NULL, 'ORD20240118001');

-- 购买记录数据
INSERT INTO `user_purchase` (`user_id`, `product_id`, `purchase_time`, `price`, `quantity`) VALUES
(4, 1, '2024-01-15 10:35:00', 2899.00, 1),
(5, 2, '2024-01-16 14:25:00', 8999.00, 1),
(4, 3, '2024-01-17 09:20:00', 1599.00, 2);

SET FOREIGN_KEY_CHECKS = 1;

-- 验证数据
SELECT '=== 数据库创建完成 ===' as info;
SELECT '用户统计:' as info, role, COUNT(*) as count FROM user GROUP BY role;
SELECT '生产商统计:' as info, COUNT(*) as count FROM manufacturer;
SELECT '商品统计:' as info, COUNT(*) as count FROM product;
SELECT '分类统计:' as info, COUNT(*) as count FROM category;
