-- 权限管理系统完整建表SQL语句
-- 数据库：springbootds
-- 执行前请确保数据库已创建：CREATE DATABASE IF NOT EXISTS springbootds DEFAULT CHARSET utf8mb4;

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 使用数据库
USE springbootds;

-- ----------------------------
-- 1. 用户表
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `register_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `status` int(1) DEFAULT '0' COMMENT '用户状态(0-正常,1-禁用)',
  `avatar` varchar(255) DEFAULT NULL COMMENT '用户头像地址',
  `address` varchar(255) DEFAULT NULL COMMENT '收货地址',
  `nickname` varchar(50) DEFAULT NULL COMMENT '用户昵称',
  `role` varchar(20) DEFAULT 'CUSTOMER' COMMENT '用户角色(CUSTOMER-普通用户,MANUFACTURER-生产商,ADMIN-管理员)',
  `manufacturer_id` int(11) DEFAULT NULL COMMENT '生产商ID(仅当role为MANUFACTURER时有值)',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_manufacturer_id` (`manufacturer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ----------------------------
-- 2. 生产商表
-- ----------------------------
DROP TABLE IF EXISTS `manufacturer`;
CREATE TABLE `manufacturer` (
  `manufacturer_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '生产商ID',
  `manufacturer_name` varchar(100) NOT NULL COMMENT '生产商名称',
  `description` text COMMENT '生产商描述',
  `logo_url` varchar(255) DEFAULT NULL COMMENT '生产商logo',
  `contact_info` varchar(255) DEFAULT NULL COMMENT '联系方式',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `website` varchar(255) DEFAULT NULL COMMENT '官网',
  `status` int(1) DEFAULT '0' COMMENT '状态(0-正常,1-禁用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` int(11) NOT NULL COMMENT '创建者用户ID',
  PRIMARY KEY (`manufacturer_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_user` (`create_user`),
  KEY `idx_manufacturer_name` (`manufacturer_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产商表';

-- ----------------------------
-- 3. 商品分类表
-- ----------------------------
DROP TABLE IF EXISTS `category`;
CREATE TABLE `category` (
  `category_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(50) NOT NULL COMMENT '分类名称',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `create_user` int(11) NOT NULL COMMENT '创建用户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`category_id`),
  KEY `idx_create_user` (`create_user`),
  KEY `idx_category_name` (`category_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表';

-- ----------------------------
-- 4. 商品表
-- ----------------------------
DROP TABLE IF EXISTS `product`;
CREATE TABLE `product` (
  `product_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称',
  `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
  `manufacturer_id` int(11) DEFAULT NULL COMMENT '生产商ID',
  `price` decimal(10,2) DEFAULT NULL COMMENT '价格',
  `stock_quantity` int(11) DEFAULT '0' COMMENT '库存数量',
  `description` text COMMENT '商品描述',
  `image_path` varchar(255) DEFAULT NULL COMMENT '商品图片路径',
  `status` int(1) DEFAULT '0' COMMENT '商品状态(0-正常,1-下架)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`product_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_manufacturer_id` (`manufacturer_id`),
  KEY `idx_product_name` (`product_name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

-- ----------------------------
-- 5. 购物车表
-- ----------------------------
DROP TABLE IF EXISTS `cart`;
CREATE TABLE `cart` (
  `cart_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '购物车ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `product_id` int(11) NOT NULL COMMENT '商品ID',
  `quantity` int(11) DEFAULT '1' COMMENT '数量',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`cart_id`),
  UNIQUE KEY `uk_user_product` (`user_id`,`product_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='购物车表';

-- ----------------------------
-- 6. 订单表
-- ----------------------------
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `product_id` int(11) NOT NULL COMMENT '商品ID',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称',
  `price` bigint(20) NOT NULL COMMENT '订单金额(分)',
  `status` int(1) DEFAULT '0' COMMENT '订单状态(0-待付款,1-已付费)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- ----------------------------
-- 7. 用户购买记录表
-- ----------------------------
DROP TABLE IF EXISTS `user_purchase`;
CREATE TABLE `user_purchase` (
  `purchase_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '购买记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `product_id` int(11) NOT NULL COMMENT '商品ID',
  `quantity` int(11) NOT NULL COMMENT '购买数量',
  `purchase_price` decimal(10,2) NOT NULL COMMENT '购买价格',
  `total_amount` decimal(10,2) NOT NULL COMMENT '总金额',
  `purchase_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '购买时间',
  `status` int(1) DEFAULT '0' COMMENT '订单状态(0-待支付,1-已支付,2-已发货,3-已完成,4-已取消)',
  PRIMARY KEY (`purchase_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_purchase_time` (`purchase_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户购买记录表';

-- ----------------------------
-- 7. 收藏表
-- ----------------------------
DROP TABLE IF EXISTS `collect`;
CREATE TABLE `collect` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `product_id` int(11) NOT NULL COMMENT '商品ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_product` (`user_id`,`product_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品收藏表';

-- ----------------------------
-- 8. 用户会话表（可选）
-- ----------------------------
DROP TABLE IF EXISTS `user_session`;
CREATE TABLE `user_session` (
  `session_id` varchar(100) NOT NULL COMMENT '会话ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `token` varchar(500) NOT NULL COMMENT 'JWT Token',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `status` int(1) DEFAULT '0' COMMENT '状态(0-有效,1-无效)',
  PRIMARY KEY (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_token` (`token`(255)),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户会话表';

-- ----------------------------
-- 添加外键约束（可选，根据需要决定是否添加）
-- ----------------------------
-- ALTER TABLE `user` ADD CONSTRAINT `fk_user_manufacturer` FOREIGN KEY (`manufacturer_id`) REFERENCES `manufacturer` (`manufacturer_id`);
-- ALTER TABLE `manufacturer` ADD CONSTRAINT `fk_manufacturer_user` FOREIGN KEY (`create_user`) REFERENCES `user` (`user_id`);
-- ALTER TABLE `category` ADD CONSTRAINT `fk_category_user` FOREIGN KEY (`create_user`) REFERENCES `user` (`user_id`);
-- ALTER TABLE `product` ADD CONSTRAINT `fk_product_category` FOREIGN KEY (`category_id`) REFERENCES `category` (`category_id`);
-- ALTER TABLE `product` ADD CONSTRAINT `fk_product_manufacturer` FOREIGN KEY (`manufacturer_id`) REFERENCES `manufacturer` (`manufacturer_id`);
-- ALTER TABLE `cart` ADD CONSTRAINT `fk_cart_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`);
-- ALTER TABLE `cart` ADD CONSTRAINT `fk_cart_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`product_id`);
-- ALTER TABLE `user_purchase` ADD CONSTRAINT `fk_purchase_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`);
-- ALTER TABLE `user_purchase` ADD CONSTRAINT `fk_purchase_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`product_id`);
-- ALTER TABLE `collect` ADD CONSTRAINT `fk_collect_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`);
-- ALTER TABLE `collect` ADD CONSTRAINT `fk_collect_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`product_id`);

SET FOREIGN_KEY_CHECKS = 1;
