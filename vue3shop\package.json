{"name": "vue3shop", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.9.0", "core-js": "^3.8.3", "element-china-area-data": "^6.1.0", "element-plus": "^2.9.10", "lodash": "^4.17.21", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "vue": "^3.2.13", "vue-router": "^4.2.5"}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@vitejs/plugin-vue": "^4.5.0", "sass": "^1.89.0", "vite": "^4.5.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}