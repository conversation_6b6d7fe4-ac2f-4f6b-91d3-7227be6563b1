package com.itheima.springbootcd.controller;

import com.itheima.springbootcd.pojo.Address;
import com.itheima.springbootcd.pojo.Result;
import com.itheima.springbootcd.service.IAddressService;
import jakarta.annotation.Resource;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Update;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/address")
public class AddressController {
    @Resource
    private IAddressService addressService;

    @GetMapping("/list")
    public Result<List<Address>> list(){
        return Result.success(addressService.list());
    }

    @PostMapping("/add")
    public Result add(@RequestBody Address address){
        addressService.insert(address);
        return Result.success();
    }

    @DeleteMapping("delete/{addressId}")
    public Result delete(@PathVariable Integer addressId){
        addressService.removeById(addressId);
        return Result.success();
    }

    @PutMapping("update/{id}")
    public Result update(@PathVariable Integer id,@RequestBody Address address){
        addressService.updateById(id,address);
        return Result.success();
    }

    @PutMapping("{addressId}/is_default")
    public Result isDefault(@PathVariable Integer addressId){
        System.out.println(addressId);
        addressService.updateDefaultByAid(addressId);
        return Result.success();
    }

}

