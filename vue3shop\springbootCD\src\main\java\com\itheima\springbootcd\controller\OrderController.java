package com.itheima.springbootcd.controller;

import com.itheima.springbootcd.pojo.Order;
import com.itheima.springbootcd.pojo.Result;
import com.itheima.springbootcd.service.IOrderService;
import com.itheima.springbootcd.utils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/order")
public class OrderController {

    @Autowired
    private IOrderService orderService;


    /**
     * 支付订单
     * @param orderId 订单ID
     * @return 支付结果
     */
    @PutMapping("/{orderId}/pay")
    public Result payOrder(@PathVariable Long orderId) {
        boolean success = orderService.payOrder(orderId);
        return success ? Result.success("订单支付成功") : Result.error("订单支付失败或订单不存在");
    }

    /**
     * 获取所有订单
     * @return 订单列表
     */
    @GetMapping("/all")
    public Result getAllOrders() {
        List<Order> orders = orderService.getAllOrders();
        return Result.success(orders);
    }

    /**
     * 根据状态查询订单
     * @param status 订单状态 (0-待支付, 1-已支付)
     * @return 订单列表
     */
    @GetMapping("/status/{status}")
    public Result getOrdersByStatus(@PathVariable Integer status) {
        List<Order> orders = orderService.getOrdersByStatus(status);
        return Result.success(orders);
    }

    // 支持 POST JSON 下单
    @PostMapping("/create")
    public Result createOrder(@RequestBody Order order) {
        // 从ThreadLocal获取当前用户ID
        Map<String, Object> map = ThreadLocalUtil.get();
        Integer userId = (Integer) map.get("id");
        order.setUserId(userId);
        orderService.createOrder(order);
        return Result.success("订单创建成功");
    }


}