package com.itheima.springbootcd.controller;

import com.itheima.springbootcd.pojo.Result;
import com.itheima.springbootcd.pojo.UserPurchase;
import com.itheima.springbootcd.service.IUserPurchaseService;
import com.itheima.springbootcd.utils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@RestController
@RequestMapping("/userPurchase")
public class UserPurchaseController {

    @Autowired
    private IUserPurchaseService userPurchaseService;

    // 结算下单接口
    @PostMapping("/checkout")
    public Result checkout(@RequestBody UserPurchase userPurchase) {
        Map<String, Object> map = ThreadLocalUtil.get();
        Integer userId = (Integer) map.get("id");
        userPurchase.setUserId(userId);
        userPurchase.setPurchaseTime(LocalDateTime.now());
        userPurchaseService.save(userPurchase);
        return Result.success();
    }
}
