package com.itheima.springbootcd.mapper;

import com.itheima.springbootcd.pojo.Address;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface AddressMapper {
    /**
     * 新增地址
     * @param entity
     * @return
     */
    @Insert("INSERT INTO address(user_id,user_phone,user_nickname,address,is_default)"+
            "VALUES (#{userId},#{userPhone},#{userNickname},#{address},#{isDefault})")
    int insert(Address entity);

    /**
     * 删除地址
     * @param id
     */
    @Delete("DELETE FROM address where address_id = #{addressId}")
    void deleteById(Integer id);

    /**
     * 编辑地址
     * @param addressId
     */
    @Update("UPDATE address SET user_nickname = #{addressData.userNickname},"+
            "user_phone = #{addressData.userPhone},"+
            "address = #{addressData.address},"+
            "is_default = #{addressData.isDefault} Where address_id = #{addressId}"
    )
    void updateById(
            @Param("addressId") Integer addressId,
            @Param("addressData") Address addressData
    );

    @Select("SELECT * FROM address WHERE user_id = #{userId}")
    List<Address> list(Integer userId);

    @Update("UPDATE address SET is_default = 0 WHERE user_id = #{userId}")
    void updateNonDefault(@Param("userId") Integer userId);

    @Update("UPDATE address SET is_default = 1 WHERE address_id = #{addressId}")
    void updateDefaultByAid(
            @Param("addressId") Integer addressId,
            @Param("userId") Integer userId
    );

}
