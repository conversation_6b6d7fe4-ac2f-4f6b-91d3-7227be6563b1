package com.itheima.springbootcd.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.itheima.springbootcd.pojo.Category;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Mapper
public interface CategoryMapper extends BaseMapper<Category> {
    @Insert("insert into category(category_name,description,create_user,create_time,update_time)"+
            "values (#{categoryName},#{description},#{createUser},#{createTime},#{updateTime})"
    )
    void add(Category category);
    @Select("select * from category where create_user=#{userId}")
    List<Category> list(Integer userId);

    @Select("select * from category order by create_time desc")
    List<Category> listAll();

    @Select("select * from category where category_id=#{id}")
    Category findById(Integer id);
    @Update("update category set category_name=#{categoryName},description=#{description},update_time=#{updateTime} where category_id=#{categoryId}")
    void update(Category category);
    @Delete("delete from category where category_id=#{categoryId}")
    void deleteById(Integer categoryId);
    @Select("select * from category where category_name like concat('%', #{categoryName}, '%') and create_user=#{userId}")
    List<Category> searchByCategoryName(String categoryName, Integer userId);
}

