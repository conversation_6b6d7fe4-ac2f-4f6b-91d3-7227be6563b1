<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.itheima.springbootcd.mapper.OrderMapper">

    <!-- 基础结果映射（与注解配置配合使用） -->
    <resultMap id="BaseResultMap" type="com.itheima.springbootcd.pojo.Order">
        <id column="id" property="id" />
        <result column="product_id" property="productId" />
        <result column="product_name" property="productName" />
        <result column="price" property="price" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="pay_time" property="payTime" />
    </resultMap>

    <!-- 批量插入订单（补充注解未实现的功能） -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO orders(product_id, product_name, price, status, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.productId}, #{item.productName}, #{item.price}, 0, NOW())
        </foreach>
    </insert>

    <!-- 复杂条件查询（示例） -->
    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT * FROM orders
        <where>
            <if test="productId != null">
                AND product_id = #{productId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="startTime != null and endTime != null">
                AND create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 统计订单金额（示例） -->
    <select id="sumPriceByStatus" resultType="java.lang.Long">
        SELECT SUM(price) FROM orders
        WHERE status = #{status}
    </select>

</mapper>