package com.itheima.springbootcd.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <p>
 * 地址实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Getter
@Setter
@ToString
@TableName("address")
public class Address implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "address_id", type = IdType.AUTO)
    private Integer addressId;

    @TableField("user_id")
    private Integer userId;

    @TableField("address")
    private String address;

    @TableField(value="is_default",property = "isDefault")
    private Boolean isDefault;

    @TableField("user_nickname")
    private String userNickname;

    @TableField("user_phone")
    private String userPhone;
}
