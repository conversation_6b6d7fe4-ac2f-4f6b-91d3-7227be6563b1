package com.itheima.springbootcd.service;

import com.itheima.springbootcd.pojo.Address;
import com.itheima.springbootcd.pojo.Result;

import java.util.List;

public interface IAddressService {

    Result insert(Address entity);
    void removeById(Integer addressId);
    Result updateById(Integer addressId,Address entity);
    List<Address> list();

    Result updateNonDefault(Integer userId);

    Result updateDefaultByAid(Integer adddressId);


}
