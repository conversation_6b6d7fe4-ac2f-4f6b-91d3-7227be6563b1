package com.itheima.springbootcd.service.impl;

import com.itheima.springbootcd.mapper.AddressMapper;
import com.itheima.springbootcd.pojo.Address;
import com.itheima.springbootcd.pojo.Result;
import com.itheima.springbootcd.service.IAddressService;
import com.itheima.springbootcd.utils.ThreadLocalUtil;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public class AddressServiceImpl implements IAddressService {
    @Resource
    private AddressMapper addressMapper;


    @Override
    public Result insert(Address entity) {
        Map<String,Object> map = ThreadLocalUtil.get();
        Integer userId = (Integer) map.get("id");
        entity.setUserId(userId);
        addressMapper.insert(entity);
        return Result.success();
    }


    @Override
    public void removeById(Integer addressId) {
        addressMapper.deleteById(addressId);
    }

    @Override
    public Result updateById(Integer addressId, Address entity) {
        addressMapper.updateById(addressId,entity);
        return Result.success();
    }

    @Override
    public List<Address> list() {
        Map<String,Object> map = ThreadLocalUtil.get();
        Integer userId = (Integer) map.get("id");
        return addressMapper.list(userId);
    }

    @Override
    public Result updateNonDefault(Integer userId) {
        addressMapper.updateNonDefault(userId);
        return Result.success();
    }

    @Override
    public Result updateDefaultByAid(Integer adddressId) {
        Map<String,Object> map = ThreadLocalUtil.get();
        Integer userId = (Integer) map.get("id");
        addressMapper.updateNonDefault(userId);
        addressMapper.updateDefaultByAid(adddressId,userId);
        return Result.success();
    }


}
