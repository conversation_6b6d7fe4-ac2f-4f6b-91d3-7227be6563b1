package com.itheima.springbootcd.service.impl;

import com.itheima.springbootcd.pojo.Category;
import com.itheima.springbootcd.mapper.CategoryMapper;
import com.itheima.springbootcd.service.ICategoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itheima.springbootcd.utils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Service
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category> implements ICategoryService {
    @Autowired
    private CategoryMapper categoryMapper;
    @Override
    //添加分类信息
    public void add(Category category) {
        Map<String,Object> map = ThreadLocalUtil.get();
        Integer userId = (Integer) map.get("id");

        // 添加调试日志
        System.out.println("Current user info from ThreadLocal: " + map);
        System.out.println("User ID: " + userId);

        if (userId == null) {
            throw new RuntimeException("用户未登录或登录已过期");
        }

        category.setCreateTime(LocalDateTime.now());
        category.setUpdateTime(LocalDateTime.now());
        category.setCreateUser(userId);
        categoryMapper.add(category);
    }
    //获取文章列表（仅当前用户创建的）
    @Override
    public List<Category> list() {
        Map<String,Object> map = ThreadLocalUtil.get();
        Integer userId = (Integer) map.get("id");
        return categoryMapper.list(userId);
    }

    //获取所有分类列表（所有用户都可以查看）
    @Override
    public List<Category> listAll() {
        return categoryMapper.listAll();
    }
    //通过id查询分类信息
    @Override
    public Category findById(Integer id) {
        Category c = categoryMapper.findById(id);
        return  c;
    }
    //修改分类信息
    @Override
    public void update(Category category) {
        category.setUpdateTime(LocalDateTime.now());
        categoryMapper.update(category);
    }
    @Override
    public void delete(Integer categoryId) {
        categoryMapper.deleteById(categoryId);
    }

    @Override
    public List<Category> search(String categoryName) {
        Map<String,Object> map = ThreadLocalUtil.get();
        Integer userId = (Integer) map.get("id");
        return categoryMapper.searchByCategoryName(categoryName, userId);
    }
}
