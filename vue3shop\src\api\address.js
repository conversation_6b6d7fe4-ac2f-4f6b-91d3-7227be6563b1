import request from '@/utils/request'
import { useUserStore } from '@/stores/user'

// 获取地址列表
export const addressListService = () => {
  const userStore = useUserStore()
  return request.get('/address/list', {
    headers: {
      'Authorization': userStore.token
    }
  })
}

// 添加地址
export const addressAddService = (addressData) => {
  const userStore = useUserStore()
  return request.post('/address/add', addressData, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': userStore.token
    }
  })
}

// 删除地址
export const addressDeleteService = (addressId) => {
  const userStore = useUserStore()
  return request.delete(`/address/delete/${addressId}`, {}, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': userStore.token
    }
  })
}

// 更新地址
export const addressUpdateService = (addressId, addressData) => {
  const userStore = useUserStore()
  return request.put(`/address/update/${addressId}`, addressData, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': userStore.token
    }
  })
}

// 设置默认地址
export const addressSetDefaultService = (addressId) => {
  const userStore = useUserStore()
  return request.put(`/address/${addressId}/is_default`, {}, {
    headers: {
      'Authorization': userStore.token
    }
  })
}