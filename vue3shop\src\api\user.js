//导入request.js请求工具
import request from '@/utils/request'

//提供调用注册接口的函数
export const userRegisterService = (registerData) => {
    // 使用URLSearchParams构建表单数据
    const params = new URLSearchParams()
    params.append('username', registerData.username)
    params.append('password', registerData.password)

    return request({
        url: '/user/register',
        method: 'post',
        data: params,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}

//提供调用登录接口的函数
export const userLoginService = (loginData) => {
    const params = new URLSearchParams()
    params.append('username', loginData.username)
    params.append('password', loginData.password)

    return request({
        url: '/user/login',
        method: 'post',
        data: params,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}

//提供调用获取用户信息接口的函数
export const userInfoService = () => {
    return request({
        url: '/user/userInfo',
        method: 'get',
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

//补充获取用户信息接口（如未有）
export function getUserInfoService() {
  return request.get('/user/userInfo')
}

//修改个人信息
export const userInfoUpdateService = (userInfoData)=>{
    return request.put('/user/update',userInfoData)
}
//修改头像
export const userAvatarUpdateService = (avatarUrl)=>{
    const params = new URLSearchParams()
    params.append('avatarUrl',avatarUrl)
    return request.patch('/user/updateAvatar',params)
}

//修改密码
export const userPasswordUpdateService = (passwordData) => {
    return request.patch('/user/updatePwd', passwordData, {
        headers: {
            'Content-Type': 'application/json'
        }
    })
}