import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ElMessage} from 'element-plus'
import { 
  addressListService, 
  addressAddService, 
  addressDeleteService, 
  addressUpdateService ,
  addressSetDefaultService
} from '@/api/address'
import { useUserStore } from '@/stores/user'

export const useAddressStore = defineStore('address', () => {
  const addressList = ref([])

  // 获取地址列表
  const getAddressList = async () => {
    try {
      const response = await addressListService()
      console.log('地址列表响应:', response) // 调试日志
      
      if (response.code === 0) {
        const formattedData = response.data.map(address => ({
          addressId: address.addressId,
          name: address.name || address.userNickname, // 兼容不同字段名
          phone: address.phone || address.userPhone,
          province: address.province,
          city: address.city,
          district: address.district,
          detail: address.detail || address.address, // 兼容不同字段名
          isDefault: address.isDefault || address.is_default // 兼容不同字段名
        }))
        
        // 将默认地址排序到最前面
        const sortedData = [...formattedData].sort((a, b) => {
          if (a.isDefault && !b.isDefault) return -1
          if (!a.isDefault && b.isDefault) return 1
          return 0
        })
        
        console.log('格式化并排序后的地址数据:', sortedData) // 调试日志
        addressList.value = sortedData
        return true
      } else {
        ElMessage.error(response.message || '获取地址列表失败')
        return false
      }
    } catch (error) {
      console.error('获取地址列表错误:', error)
      ElMessage.error('获取地址列表失败')
      return false
    }
  }

  // 添加地址
  const addAddress = async (addressData) => {
    const userStore = useUserStore()
    if (!userStore.token) {
      ElMessage.error('请先登录')
      return false
    }

    try {
      const response = await addressAddService(addressData)
      if (response.code === 0) {
        ElMessage.success('地址添加成功')
        await getAddressList() // 刷新地址列表
        return true
      } else {
        ElMessage.error(response.message || '地址添加失败')
        return false
      }
    } catch (error) {
      console.error('添加地址失败:', error)
      if (error.response?.status === 401) {
        ElMessage.error('登录已过期，请重新登录')
        userStore.logout()
        return false
      }
      ElMessage.error('地址添加失败')
      return false
    }
  }

  // 更新地址
  const updateAddress = async (id, addressData) => {
    const userStore = useUserStore()
    if (!userStore.token) {
      ElMessage.error('请先登录')
      return false
    }

    try {
      const response = await addressUpdateService(id, addressData)
      if (response.code === 0) {
        ElMessage.success('地址更新成功')
        await getAddressList() // 刷新地址列表
        return true
      } else {
        ElMessage.error(response.message || '地址更新失败')
        return false
      }
    } catch (error) {
      console.error('更新地址失败:', error)
      if (error.response?.status === 401) {
        ElMessage.error('登录已过期，请重新登录')
        userStore.logout()
        return false
      }
      ElMessage.error('地址更新失败')
      return false
    }
  }

  // 删除地址
  const deleteAddress = async (id) => {
    const userStore = useUserStore()
    if (!userStore.token) {
      ElMessage.error('请先登录')
      return false
    }

    try {
      const response = await addressDeleteService(id)
      if (response.code === 0) {
        ElMessage.success('地址删除成功')
        await getAddressList() // 刷新地址列表
        return true
      } else {
        ElMessage.error(response.message || '地址删除失败')
        return false
      }
    } catch (error) {
      console.error('删除地址失败:', error)
      if (error.response?.status === 401) {
        ElMessage.error('登录已过期，请重新登录')
        userStore.logout()
        return false
      }
      ElMessage.error('地址删除失败')
      return false
    }
  }

  // 设置默认地址
const setDefaultAddress = async (addressId) => {
  const userStore = useUserStore()
  if (!userStore.token) {
    ElMessage.error('请先登录')
    return false
  }

  try {
    const response = await addressSetDefaultService(addressId)
    if (response.code === 0) {
      ElMessage.success('默认地址设置成功')
      await getAddressList() // 刷新地址列表
      return true
    } else {
      ElMessage.error(response.message || '默认地址设置失败')
      return false
    }
  } catch (error) {
    console.error('设置默认地址失败:', error)
    if (error.response?.status === 401) {
      ElMessage.error('登录已过期，请重新登录')
      userStore.logout()
      return false
    }
    ElMessage.error('默认地址设置失败')
    return false
  }
}


  return {
    addressList,
    getAddressList,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress
  }
}, {
  persist: {
    key: 'address-store',
    storage: localStorage,
    paths: ['addressList']
  }
})
