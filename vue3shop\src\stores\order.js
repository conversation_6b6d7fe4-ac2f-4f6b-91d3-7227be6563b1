import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'  // 使用统一的request工具
import { useUserStore } from '@/stores/user'

export const useOrderStore = defineStore('order', () => {
  const unpaidOrders = ref([])
  const paidOrders = ref([])
  const allOrders = ref([])
  const userStore = useUserStore()
  
  // 创建订单
  const createOrder = async (orderData) => {
    try {
      // 检查登录状态
      if (!userStore.token) {
        ElMessage.warning('请先登录')
        return false
      }

      // 计算总金额
      const totalPrice = orderData.price * orderData.quantity
      console.log('Order data:', orderData);
      console.log('Product name:', orderData.product_name);
      const response = await request.post('/order/create', null, {
        params: {  // 关键修改：参数通过URL传递
          product_id: orderData.product_id,
          product_name: orderData.product_name,
          price: totalPrice
        }
      })
      if (response.code === 0) {
        ElMessage.success('订单创建成功')
        return response.data
      } else {
        ElMessage.error(response.message || '订单创建失败')
        return false
      }
    } catch (error) {
      console.error('创建订单错误:', error)
    
      return false
    }
  }
  
  // 支付订单
  const payOrder = async (orderId) => {
    try {
      const response = await request.put(`/order/${orderId}/pay`)
      
      if (response.code === 0) {
        ElMessage.success('支付成功')
        return response.data
      } else {
        ElMessage.error(response.message || '支付失败')
        return false
      }
    } catch (error) {
      console.error('支付错误:', error)
      ElMessage.error('支付失败')
      return false
    }
  }
  
  // 获取待付款订单
  const fetchUnpaidOrders = async () => {
    try {
      const response = await request.get('/order/status/0')
      
      if (response.code === 0) {
        unpaidOrders.value = response.data
        return true
      } else {
        ElMessage.error(response.message || '获取待付款订单失败')
        return false
      }
    } catch (error) {
      console.error('获取待付款订单错误:', error)
      ElMessage.error('获取待付款订单失败')
      return false
    }
  }
  
  // 获取已付款订单
  const fetchPaidOrders = async () => {
    try {
      const response = await request.get('/order/status/1')
      
      if (response.code === 0) {
        paidOrders.value = response.data
        return true
      } else {
        ElMessage.error(response.message || '获取已付款订单失败')
        return false
      }
    } catch (error) {
      console.error('获取已付款订单错误:', error)
      ElMessage.error('获取已付款订单失败')
      return false
    }
  }

  // 获取全部订单
  const fetchAllOrders = async () => {
    try {
      const response = await request.get('/order/all')
      
      if (response.code === 0) {
        allOrders.value = response.data
        console.log("这是我的数据：",allOrders.value)
        return true
      } else {
        ElMessage.error(response.message || '获取订单列表失败')
        return false
      }
    } catch (error) {
      console.error('获取订单列表错误:', error)
      ElMessage.error('获取订单列表失败')
      return false
    }
  }
  
  return {
    unpaidOrders,
    paidOrders,
    allOrders,
    createOrder,
    payOrder,
    fetchUnpaidOrders,
    fetchPaidOrders,
    fetchAllOrders
  }
})