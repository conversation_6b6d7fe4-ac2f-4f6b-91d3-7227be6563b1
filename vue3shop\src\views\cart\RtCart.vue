<template>
  <div class="cart-page">
    <RtHeader title="购物车" />
    <div class="cart-container">
      <div v-if="cartStore.loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>
      <div v-else-if="cartStore.cartList.length === 0" class="empty-cart">
        <el-empty description="购物车是空的" />
        <el-button type="primary" @click="goShopping">去购物</el-button>
      </div>
      <div v-else class="cart-content">
        <el-card class="cart-card" v-loading="cartStore.loading">
          <template #header>
            <div class="header">
              <span class="title">我的购物车</span>
              <div class="extra">
                <el-button type="danger" @click="handleClearCart" :disabled="!cartStore.cartList.length">
                  清空购物车
                </el-button>
              </div>
            </div>
          </template>

          <div class="cart-list">
            <div v-for="item in cartStore.cartList" :key="item.cartId" class="cart-item">
              <el-checkbox v-model="item.selected" @change="handleSelectChange(item)" />
              <div class="item-info">
                <img :src="item.productImage" :alt="item.productName" class="product-image" />
                <div class="product-info">
                  <h3 class="product-name">{{ item.productName }}</h3>
                  <p class="product-price">¥{{ item.price }}</p>
                </div>
              </div>
              <div class="item-actions">
                <el-input-number
                  v-model="item.quantity"
                  :min="1"
                  :max="99"
                  @change="handleQuantityChange(item)"
                />
                <el-button
                  type="danger"
                  :icon="Delete"
                  circle
                  @click="handleRemoveItem(item)"
                />
              </div>
            </div>
          </div>

          <div class="cart-footer">
            <div class="select-all">
              <el-checkbox
                v-model="selectAll"
                @change="handleSelectAllChange"
              >
                全选
              </el-checkbox>
            </div>
            <div class="total-info">
              <span class="total-price">
                合计: <em>¥{{ totalPrice }}</em>
              </span>
              <el-button type="primary" @click="handleCheckout" :disabled="!selectedItems.length">
                结算({{ selectedItems.length }})
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    <RtTabBar />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Delete } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useCartStore } from '@/stores/cart'
import { useUserStore } from '@/stores/user'
import RtHeader from '../user/RtHeader.vue'

const router = useRouter()
const cartStore = useCartStore()
const userStore = useUserStore()
const selectAll = ref(false)

// 计算选中商品的总价
const totalPrice = computed(() => {
  console.log('购物车列表:', cartStore.cartList)
  console.log('选中的商品:', cartStore.cartList.filter(item => item.selected))
  const total = cartStore.cartList
    .filter(item => item.selected)
    .reduce((total, item) => {
      console.log('商品价格:', item.price, '数量:', item.quantity)
      return total + item.price * item.quantity
    }, 0)
  console.log('计算的总价:', total)
  return total.toFixed(2)
})

// 计算选中的商品数量
const selectedItems = computed(() => {
  return cartStore.cartList.filter(item => item.selected)
})


// 处理全选/取消全选
const handleSelectAllChange = (val) => {
  cartStore.cartList.forEach(item => {
    item.selected = val
  })
}

// 处理单个商品选择状态变化
const handleSelectChange = () => {
  selectAll.value = cartStore.cartList.every(item => item.selected)
}

// 处理商品数量变化
const handleQuantityChange = async (item) => {
  await cartStore.updateCartItem(item.cartId, item.quantity)
}

// 处理删除商品
const handleRemoveItem = (item) => {
  ElMessageBox.confirm(
    `确定要删除商品"${item.productName}"吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    await cartStore.removeFromCart(item.cartId)
  }).catch(() => {
    // 取消删除
  })
}

// 处理清空购物车
const handleClearCart = () => {
  ElMessageBox.confirm(
    '确定要清空购物车吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    await cartStore.clearCart()
  }).catch(() => {
    // 取消清空
  })
}

// 处理结算
const handleCheckout = () => {
  if (!selectedItems.value.length) {
    return
  }
  // 结算时将所有选中的商品作为数组传递到结算页
  localStorage.setItem(
    'buyNowProduct',
    JSON.stringify(
      selectedItems.value.map(item => ({
        ...item,
        image: item.productImage // 兼容结算页的图片字段
      }))
    )
  )
  router.push('/checkout?type=buynow')
}

// 跳转到首页
const goShopping = () => {
  router.push('/')
}

onMounted(async () => {
  console.log('购物车页面加载，检查登录状态')
  console.log('当前token:', userStore.token)
  console.log('当前用户信息:', userStore.userInfo)
  
  if (!userStore.token) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  
  try {
    console.log('开始获取购物车列表')
    const result = await cartStore.getCartList()
    console.log('获取购物车列表结果:', result)
  } catch (error) {
    console.error('加载购物车失败:', error)
    ElMessage.error('加载购物车失败')
  }
})
</script>

<style lang="scss" scoped>
.cart-page {
  min-height: 100vh;
  background: #f6f8fa;
}

.cart-container {
  padding: 20px;
  min-height: 400px;
}

.loading-container {
  padding: 20px;
}

.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  gap: 20px;
}

.cart-content {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.cart-card {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px #e3f2fd;
  
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .title {
      font-size: 18px;
      font-weight: bold;
      color: #1565c0;
    }
  }
}

.cart-list {
  .cart-item {
    display: flex;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid #eee;
    
    &:last-child {
      border-bottom: none;
    }
    
    .item-info {
      display: flex;
      align-items: center;
      flex: 1;
      margin: 0 20px;
      
      .product-image {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 8px;
        margin-right: 20px;
      }
      
      .product-info {
        flex: 1;
        
        .product-name {
          font-size: 16px;
          color: #333;
          margin: 0 0 10px;
        }
        
        .product-price {
          color: #f56c6c;
          font-size: 18px;
          font-weight: bold;
          margin: 0;
        }
      }
    }
    
    .item-actions {
      display: flex;
      align-items: center;
      gap: 20px;
    }
  }
}

.cart-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 0;
  margin-top: 20px;
  border-top: 1px solid #eee;
  
  .total-info {
    display: flex;
    align-items: center;
    gap: 20px;
    
    .total-price {
      font-size: 16px;
      
      em {
        color: #f56c6c;
        font-size: 24px;
        font-style: normal;
        font-weight: bold;
      }
    }
  }
}
</style>