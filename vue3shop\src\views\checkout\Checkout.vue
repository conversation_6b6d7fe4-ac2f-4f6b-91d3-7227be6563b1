<template>
  <div class="checkout-container">
    <h2>确认订单</h2>
    <div class="address-section">
      <h3>收货地址</h3>
      <div v-if="addressStore.addressList.length">
        <el-select v-model="selectedAddressId" placeholder="请选择收货地址" style="width: 100%; margin-bottom: 10px;">
          <el-option
            v-for="addr in addressStore.addressList"
            :key="addr.addressId"
            :label="`${addr.name}（${maskPhone(addr.phone)}） ${addr.isDefault ? '[默认地址]' : ''}`"
            :value="addr.addressId"
          >
            <span>
              {{ addr.name }}（{{ maskPhone(addr.phone) }}）
              <span v-if="addr.isDefault" style="color: #409eff; font-size: 12px; margin-left: 8px;">默认地址</span>
            </span>
            <div style="font-size: 12px; color: #888;">
              {{ addr.province }}{{ addr.city }}{{ addr.district }}{{ addr.detail }}
            </div>
          </el-option>
        </el-select>
        <div style="display: flex; align-items: center;">
          <div>
            {{ selectedAddress.name }}（{{ maskPhone(selectedAddress.phone) }}）
            <span v-if="selectedAddress.isDefault" style="color: #409eff; font-size: 12px; margin-left: 8px;">默认地址</span>
          </div>
          <el-button
            type="text"
            style="margin-left: 12px;"
            @click="goEditAddress"
          >编辑地址</el-button>
        </div>
        <div>
          {{ selectedAddress.province }}{{ selectedAddress.city }}{{ selectedAddress.district }}{{ selectedAddress.detail }}
        </div>
      </div>
      <div v-else>
        <el-empty description="暂无收货地址，请先添加" />
      </div>
    </div>
    <div class="product-section" v-if="products.length">
      <h3>商品信息</h3>
      <div
        class="product-detail product-detail-divider"
        v-for="(item, idx) in products"
        :key="item.cartId || item.productId"
        :class="{ 'with-divider': idx !== products.length - 1 }"
      >
        <img :src="item.image || item.productImage" alt="商品图片" class="product-img"/>
        <div class="product-info">
          <div>名称：{{ item.productName }}</div>
          <div>单价：¥{{ item.price }}</div>
          <div>数量：{{ item.quantity }}</div>
          <div>总价：¥{{ (item.price * item.quantity).toFixed(2) }}</div>
        </div>
      </div>
    </div>
    <div class="checkout-actions">
      <el-button type="primary" @click="submitOrder" :loading="loading">提交订单</el-button>
      <el-button @click="cancelOrder">取消</el-button>
    </div>
    <el-dialog v-model="dialogVisible" title="支付结果" width="30%">
      <div v-if="paySuccess">支付成功！</div>
      <div v-else>支付失败，订单未提交。</div>
      <template #footer>
        <el-button type="primary" @click="goOrderPage">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { createOrderService } from '@/api/order'
import { useAddressStore } from '@/stores/address'

const router = useRouter()
const product = ref(null)
const loading = ref(false)
const dialogVisible = ref(false)
const paySuccess = ref(false)

// 地址相关
const addressStore = useAddressStore()
const selectedAddressId = ref(null)
const selectedAddress = computed(() => {
  return addressStore.addressList.find(a => a.addressId === selectedAddressId.value) || {}
})
const defaultAddress = computed(() => {
  if (!addressStore.addressList.length) return null
  return addressStore.addressList.find(a => a.isDefault) || addressStore.addressList[0]
})

const products = ref([])

const totalPrice = computed(() => {
  return products.value.reduce((sum, item) => sum + item.price * item.quantity, 0).toFixed(2)
})

onMounted(async () => {
  // 获取商品信息
  const buyNowProduct = localStorage.getItem('buyNowProduct')
  if (buyNowProduct) {
    let parsed = JSON.parse(buyNowProduct)
    // 兼容单商品和多商品
    products.value = Array.isArray(parsed) ? parsed : [parsed]
  }
  // 获取地址列表
  await addressStore.getAddressList()
  // 设置初始选中地址为默认地址
  if (addressStore.addressList.length) {
    const def = addressStore.addressList.find(a => a.isDefault) || addressStore.addressList[0]
    selectedAddressId.value = def.addressId
  }
})

// 若地址列表变化，自动选中默认地址
watch(
  () => addressStore.addressList,
  (list) => {
    if (list.length && !selectedAddressId.value) {
      const def = list.find(a => a.isDefault) || list[0]
      selectedAddressId.value = def.addressId
    }
  },
  { immediate: true }
)

const submitOrder = async () => {
  if (!products.value.length) return
  if (!selectedAddress.value || !selectedAddress.value.addressId) {
    ElMessage.error('请先选择收货地址')
    return
  }
  loading.value = true
  try {
    // 支持多商品下单
    for (const item of products.value) {
      await createOrderService({
        productId: item.productId,
        productName: item.productName,
        price: item.price,
        quantity: item.quantity,
        address: `${selectedAddress.value.province}${selectedAddress.value.city}${selectedAddress.value.district}${selectedAddress.value.detail}`,
        receiver: selectedAddress.value.name,
        receiverPhone: selectedAddress.value.phone
      })
    }
    paySuccess.value = true
    dialogVisible.value = true
    // 清除本地 buyNowProduct
    localStorage.removeItem('buyNowProduct')
  } catch (e) {
    ElMessage.error('下单失败')
  } finally {
    loading.value = false
  }
}

const cancelOrder = () => {
  paySuccess.value = false
  dialogVisible.value = true
  router.back(-1)
}

const goOrderPage = () => {
  dialogVisible.value = false
  if (paySuccess.value) {
    router.push('/order')
  }
}

const goEditAddress = () => {
  // 跳转到地址管理页面
  router.push('/address')
}

// 手机号脱敏函数
function maskPhone(phone) {
  if (!phone) return ''
  // 只保留前三位和后四位，中间用*号替换
  return phone.replace(/^(\d{3})\d*(\d{4})$/, '$1****$2')
}
</script>

<style scoped>
.checkout-container {
  max-width: 600px;
  margin: 2rem auto;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
  padding: 2rem;
}
.address-section, .product-section {
  margin-bottom: 2rem;
}
.product-detail {
  display: flex;
  align-items: center;
  background: #fff;
}
.product-detail.with-divider {
  border-bottom: 8px solid #f7f7f7;
}
.product-img {
  width: 120px;
  height: 120px;
  object-fit: cover;
  margin-right: 1.5rem;
  border-radius: 8px;
  border: 1px solid #eee;
}
.product-info > div {
  margin-bottom: 0.5rem;
}
.checkout-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}
</style>
