<template>
  <div>
    <div class="list-items">
      <div class="list-item" @click="goCollect">
        <img :src="menuList.myCollect" />
        <span>我的收藏</span>
      </div>
      <div class="list-item" @click="goAddress">
        <img :src="menuList.myAddress" />
        <span>地址管理</span>
      </div>
      <div class="list-item" @click="goCart">
        <img :src="menuList.myCart" />
        <span>购物车</span>
      </div>
      <!-- 管理员专用菜单 -->
      <div v-if="userStore.isAdmin()" class="list-item" @click="goManufacturerManage">
        <img :src="menuList.myAddress" />
        <span>生产商管理</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import myCollect from '@/assets/images/my/我的收藏.svg'
import myAddress from '@/assets/images/my/地址管理.svg'
import myCart from '@/assets/images/my/_购物车.svg'
const menuList = {
  myCollect,
  myAddress,
  myCart
}
const router = useRouter()
const userStore = useUserStore()

function goAddress() {
  router.push('/address')
}
function goCollect() {
  router.push('/collect')
}
function goManufacturerManage() {
  router.push('/manufacturer/manage')
}

function goCart() {
  router.push('/cart')
}
</script>
<style scoped>
.list-items {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 520px;
  max-width: 700px;
  background: #fff;
  margin-left: auto;
  margin-right: 16px;
  border-radius: 18px;
  box-shadow: 0 4px 24px #e3f2fd;
  overflow: hidden;
  padding: 10px 0;
}
.list-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e3f2fd;
  position: relative;
  padding: 18px 36px 18px 18px;
  cursor: pointer;
  transition: background 0.2s;
}
.list-item:last-child {
  border-bottom: none;
}
.list-item:hover {
  background: #f5fafd;
}
.list-item img {
  width: 54px;
  height: 54px;
  margin-bottom: 0;
  padding-top: 0;
  padding-left: 0;
  border-radius: 10px;
  background: #f0f7fa;
  box-shadow: 0 1px 4px #e3f2fd;
}
.list-item span {
  margin-left: 24px;
  font-size: 22px;
  color: #1565c0;
  font-weight: 700;
  flex: 1;
  text-align: left;
}
.list-item::after {
  content: '>';
  font-size: 28px;
  color: #b0b0b0;
  margin-left: 20px;
  display: inline-block;
  min-width: 22px;
}
@media (max-width: 700px) {
  .list-items {
    min-width: unset;
    max-width: unset;
    border-radius: 10px;
    box-shadow: none;
    padding: 0 2px;
  }
  .list-item {
    padding: 10px 10px 10px 6px;
  }
  .list-item img {
    width: 32px;
    height: 32px;
  }
  .list-item span {
    font-size: 15px;
    margin-left: 10px;
  }
  .list-item::after {
    font-size: 18px;
    margin-left: 8px;
  }
}
</style>