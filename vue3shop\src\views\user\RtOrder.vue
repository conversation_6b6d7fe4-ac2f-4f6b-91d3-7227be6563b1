<template>
  <div class="my-order">
    <div class="my-order-title-row">
      <span>我的订单</span>
      <a class="my-order-all" @click.prevent="goAllOrder" href="#">全部订单</a>
    </div>
    <div class="my-order-items">
      <div class="my-order-item" @click="goUnpaidOrder">
        <img :src="MyOrder.WaitToPay" />
        <span>待付款</span>
      </div>
      <div class="my-order-item">
        <img :src="MyOrder.waitToPick" />
        <span>待收货</span>
      </div>
      <div class="my-order-item">
        <img :src="MyOrder.waitToEvaluate" />
        <span>待评价</span>
      </div>
      <div class="my-order-item">
        <img :src="MyOrder.waitToReturn" />
        <span>售后</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import WaitToPay from '@/assets/images/my/待付款.svg'
import waitToPick from '@/assets/images/my/待收货.svg'
import waitToEvaluate from '@/assets/images/my/3.1待评价.svg'
import waitToReturn from '@/assets/images/my/售后.svg'

const MyOrder = {
  WaitToPay: WaitToPay,
  waitToPick: waitToPick,
  waitToEvaluate: waitToEvaluate,
  waitToReturn: waitToReturn,
}

const router = useRouter()
function goAllOrder() {
  router.push('/order/list') 
}
function goUnpaidOrder() {
  router.push({ path: '/order/list', query: { tab: 'unpaid' } })
}
</script>
<style scoped>
.my-order {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 480px;
  max-width: 640px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px #e3f2fd;
  padding: 32px 36px 32px 36px;
  margin: 0 auto;
}
.my-order-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 22px;
  font-weight: bold;
  color: #1565c0;
  margin-bottom: 28px;
}
.my-order-all {
  font-size: 20px; /* 变大字体 */
  color: #2193b0;
  text-decoration: none;
  font-weight: bold; /* 更醒目 */
  transition: color 0.2s;
  display: flex;
  align-items: center;
}
.my-order-all::after {/*利用CSS伪元素选择器，自动在.my-order-all这个元素后面插入一个>字符 */
  content: '>';
  font-size: 26px;
  margin-left: 4px;
  color: #2193b0;
  font-weight: bold;
  display: inline-block;
  vertical-align: middle;
}
.my-order-all:hover {
  color: #e2231a;
  text-decoration: underline;
}
.my-order-items {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 0;
}
.my-order-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  cursor: pointer;
  padding: 16px 0;
  border-radius: 12px;
  transition: background 0.2s, box-shadow 0.2s;
}
.my-order-item:hover {
  background: #e3f2fd;
  box-shadow: 0 2px 8px #b2ebf2;
}
.my-order-item img {
  width: 54px;
  height: 54px;
  margin-bottom: 10px;
}
.my-order-item span {
  font-size: 18px;
  color: #333;
  margin-top: 4px;
  font-weight: 500;
}
@media (max-width: 600px) {
  .my-order {
    min-width: unset;
    max-width: unset;
    padding: 14px 4px;
  }
  .my-order-items {
    gap: 2px;
  }
  .my-order-item img {
    width: 32px;
    height: 32px;
  }
  .my-order-title-row {
    font-size: 16px;
    margin-bottom: 12px;
  }
  .my-order-item span {
    font-size: 13px;
  }
}
</style>
