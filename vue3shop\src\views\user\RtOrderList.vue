<template>
    <div class="order-container">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="全部订单" name="all">
          <el-table :data="orderStore.allOrders" style="width: 100%" v-loading="loading">
            <el-table-column prop="id" label="订单号" width="120" />
            <el-table-column prop="productName" label="商品名称" />
            <el-table-column prop="price" label="总价">
              
            </el-table-column>
            <el-table-column prop="status" label="状态">
              <template #default="scope">
                <el-tag :type="scope.row.status === 0 ? 'warning' : 'success'">
                  {{ scope.row.status === 0 ? '待付款' : '已付款' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180" />
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button 
                  v-if="scope.row.status === 0"
                  type="primary" 
                  size="small" 
                  @click="handlePay(scope.row.id)"
                >
                  支付
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
        </el-tab-pane>
        
        <el-tab-pane label="待付款" name="unpaid">
          <el-table :data="orderStore.unpaidOrders" style="width: 100%" v-loading="loading">
            <el-table-column prop="productName" label="商品名称" />
            <el-table-column prop="price" label="总价">
              
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="primary" size="small" @click="handlePay(scope.row.id)">
                  去支付
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="已付款" name="paid">
          <el-table :data="orderStore.paidOrders" style="width: 100%" v-loading="loading">
            <el-table-column prop="productName" label="商品名称" />
            <el-table-column prop="price" label="总价">
            </el-table-column>
            <el-table-column prop="payTime" label="支付时间" width="180" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, watch } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useOrderStore } from '@/stores/order'
  import { ElMessage } from 'element-plus'
  import _ from 'lodash'
  
  const orderStore = useOrderStore()
  const route = useRoute()
  const router = useRouter()
  const activeTab = ref('all')
  const loading = ref(false)
  
  // 根据路由参数初始化tab
  onMounted(async () => {
    // 读取路由参数tab，支持 ?tab=unpaid 或 ?tab=paid
    if (route.query.tab === 'unpaid') {
      activeTab.value = 'unpaid'
    } else if (route.query.tab === 'paid') {
      activeTab.value = 'paid'
    } else {
      activeTab.value = 'all'
    }
    await fetchTabData(activeTab.value)
  })
  
  const fetchTabData = async (tab) => {
  loading.value = true
  try {
    if (tab === 'all') {
      await orderStore.fetchAllOrders()
    } else if (tab === 'unpaid') {
      await orderStore.fetchUnpaidOrders()
    } else if (tab === 'paid') {
      await orderStore.fetchPaidOrders()
      // 按支付时间降序排序
      orderStore.paidOrders = _.orderBy(orderStore.paidOrders, ['payTime'], ['desc'])
    }
  } catch (error) {
    ElMessage.error('获取订单失败: ' + error.message)
  } finally {
    loading.value = false
  }
}
  
  // 监听activeTab变化，自动切换数据并同步路由参数
  watch(
    activeTab,
    (val) => {
      fetchTabData(val)
      // 同步路由参数，便于刷新/分享
      router.replace({ path: '/order/list', query: val === 'all' ? {} : { tab: val } })
    }
  )
  
  const handlePay = async (orderId) => {
    try {
      loading.value = true
      await orderStore.payOrder(orderId)
      ElMessage.success('支付成功')
      await fetchTabData(activeTab.value)
    } catch (error) {
      ElMessage.error('支付失败: ' + error.message)
    } finally {
      loading.value = false
    }
  }
  </script>
  
  <style scoped>
  .order-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
  
  .el-table {
    margin-top: 15px;
  }
  
  .el-tag {
    margin: 2px;
  }
  </style>