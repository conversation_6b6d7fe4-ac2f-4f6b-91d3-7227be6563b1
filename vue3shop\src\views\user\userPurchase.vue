<template>
  <div class="address-container">
    <!-- 返回上一页按钮 -->
    <div class="back-button">
      <el-tooltip content="返回上一页" placement="right" effect="dark">
        <el-button type="primary" :icon="ArrowLeft" circle @click="goBack" />
      </el-tooltip>
    </div>
    <div class="address-header">
      <h2>收货地址管理</h2>
    </div>

    <div class="address-list">
      <template v-if="addressStore.addressList.length > 0">
        <div v-for="address in addressStore.addressList" :key="address.addressId" class="address-card"
          :class="{ 'default-address': address.isDefault }">
          <!-- 地址信息 -->
          <div class="address-info">
            <div class="address-row">
              <span class="name">{{ address.name }}</span>
              <span class="phone">{{ address.phone }}</span>
              <span v-if="address.isDefault" class="default-tag">默认</span>
            </div>
            <div class="address-detail">
              {{ address.province }}{{ address.city }}{{ address.district }}{{ address.detail }}
            </div>
          </div>
          <!-- 操作按钮 -->
          <div class="address-actions">
              <el-button type="text" @click="setDefault(address.addressId)">
              设为默认
            </el-button>
            <el-button type="text" @click="editAddress(address)">
              编辑
            </el-button>
            <el-button type="text" @click="deleteAddress(address.addressId)">
              删除
            </el-button>
          </div>
        </div>
      </template>
      <div v-else class="empty-tip">
        <el-empty description="暂无收货地址" />
        <el-button type="primary" @click="showAddDialog" class="add-first-address">
          添加第一个地址
        </el-button>
      </div>
    </div>
  </div>

  <div class="address-footer">
    <el-button type="primary" @click="showAddDialog">
      添加新地址
    </el-button>
  </div>

  <!-- 添加地址对话框 -->
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px">
    <el-form :model="addressForm" label-width="80px">
      <el-form-item label="收货人">
        <el-input v-model="addressForm.name" />
      </el-form-item>
      <el-form-item label="手机号">
        <el-input v-model="addressForm.phone" />
      </el-form-item>
      <el-form-item label="所在地区">
        <el-cascader v-model="selectedArea" :options="regionData" @change="handleAreaChange" />
      </el-form-item>
      <el-form-item label="详细地址">
        <el-input v-model="addressForm.detail" type="textarea" />
      </el-form-item>
      <el-form-item label="默认地址">
        <el-switch v-model="addressForm.isDefault" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitAddress">
        确认
      </el-button>
    </template>
  </el-dialog>
  
  <!-- 编辑地址对话框 -->
  <el-dialog v-model="editDialogVisible" title="编辑地址" width="500px">
    <el-form :model="editForm" label-width="80px">
      <el-form-item label="收货人">
        <el-input v-model="editForm.name" />
      </el-form-item>
      <el-form-item label="手机号">
        <el-input v-model="editForm.phone" />
      </el-form-item>
      <el-form-item label="详细地址">
        <el-input v-model="editForm.detail" type="textarea" />
      </el-form-item>
      <el-form-item label="默认地址">
        <el-switch v-model="editForm.isDefault" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="editDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitEdit">
        确认
      </el-button>
    </template>
  </el-dialog>


</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { useAddressStore } from '@/stores/address'
import { regionData } from 'element-china-area-data'

const router = useRouter()
const addressStore = useAddressStore()

onMounted(async () => {
  await addressStore.getAddressList()
})

function goBack() {
  router.go(-1)
}

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('添加地址')
const isEditing = ref(false)
const currentAddressId = ref(null)
const editDialogVisible = ref(false)
const editForm = ref({
  name: '',
  phone: '',
  detail: '',
  isDefault: false
})


// 地址表单
const addressForm = ref({
  name: '',
  phone: '',
  province: '',
  city: '',
  district: '',
  detail: '',
  isDefault: false
})


// 显示添加对话框
function showAddDialog() {
  dialogTitle.value = '添加地址'
  isEditing.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑地址
const selectedArea = ref([])

function handleAreaChange(val) {
  const [provinceCode, cityCode, districtCode] = val
  const provinceObj = regionData.find(p => p.value === provinceCode)
  const cityObj = provinceObj?.children?.find(c => c.value === cityCode)
  const districtObj = cityObj?.children?.find(d => d.value === districtCode)

  addressForm.value.province = provinceObj?.label || ''
  addressForm.value.city = cityObj?.label || ''
  addressForm.value.district = districtObj?.label || ''
}

function editAddress(address) {
  currentAddressId.value = address.addressId
  editForm.value = {
    name: address.name,
    phone: address.phone,
    detail: address.detail,
    isDefault: address.isDefault || false
  }
  editDialogVisible.value = true
}

// 重置表单
function resetForm() {
  addressForm.value = {
    name: '',
    phone: '',
    region: [],
    detail: '',
    isDefault: false
  }
  selectedArea.value = [] // 新增：重置省市区选择
}

// 提交地址
async function submitAddress() {
  const addressData = {
    userNickname: addressForm.value.name,
    userPhone: addressForm.value.phone,
    address: `${addressForm.value.province}${addressForm.value.city}${addressForm.value.district}${addressForm.value.detail}`,
    isDefault: addressForm.value.isDefault
  }

  const success = await addressStore.addAddress(addressData)
  if (success) {
    dialogVisible.value = false
  }
  
}

// 提交编辑
async function submitEdit() {
  const addressData = {
    userNickname: editForm.value.name,
    userPhone: editForm.value.phone,
    address: editForm.value.detail,
    isDefault: editForm.value.isDefault
  }
  const success = await addressStore.updateAddress(currentAddressId.value, addressData)
  if (success) {
    editDialogVisible.value = false
  }
}

// 删除地址
function deleteAddress(id) {
  ElMessageBox.confirm('确定要删除该地址吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    await addressStore.deleteAddress(id)
  }).catch(() => {
    // 取消删除
  })
}

// 设置默认地址
async function setDefault(addressId) {
  const success = await addressStore.setDefaultAddress(addressId)
  if (success) {
    // 刷新地址列表
    await addressStore.getAddressList()
  }
}

</script>

<style scoped>
.back-button {
  margin-bottom: 20px;
}

.address-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.address-header {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.address-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.address-list {
  margin-bottom: 20px;
}

.address-card {
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #ebeef5;
}

.default-address {
  border-left: 3px solid #409eff;
}

.address-info {
  margin-bottom: 10px;
}

.address-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.address-row .name {
  font-weight: 500;
  margin-right: 15px;
}

.address-row .phone {
  color: #666;
  margin-right: 15px;
}

.default-tag {
  font-size: 12px;
  padding: 2px 5px;
  background-color: #409eff;
  color: #fff;
  border-radius: 3px;
}

.address-detail {
  color: #666;
  line-height: 1.5;
}

.address-actions {
  display: flex;
  justify-content: flex-end;
}

.address-footer {
  text-align: center;
  padding: 20px 0;
}

@media (max-width: 768px) {
  .address-container {
    padding: 15px;
  }

  .address-card {
    padding: 10px;
  }

  .address-actions {
    flex-direction: column;
    align-items: flex-end;
  }

  .address-actions .el-button {
    margin-bottom: 5px;
  }
}
</style>